import 'package:flutter/material.dart';
import '../../../../core/models/shift_model.dart';
import '../../../../core/theme/app_theme.dart';

class EmployeeShiftCard extends StatelessWidget {
  final ShiftModel shift;

  const EmployeeShiftCard({
    super.key,
    required this.shift,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _getStatusColor(shift.status).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getStatusIcon(shift.status),
                    color: _getStatusColor(shift.status),
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _getDayName(shift.startTime),
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: AppTheme.textPrimary,
                                ),
                      ),
                      Text(
                        _getDateString(shift.startTime),
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: AppTheme.textSecondary,
                            ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(shift.status).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    shift.status.toString().split('.').last.toUpperCase(),
                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                          color: _getStatusColor(shift.status),
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildTimeInfo(
                    context,
                    'Start',
                    '${shift.startTime.hour}:${shift.startTime.minute.toString().padLeft(2, '0')}',
                    Icons.access_time,
                  ),
                ),
                Container(
                  width: 1,
                  height: 40,
                  color: AppTheme.textTertiary.withValues(alpha: 0.3),
                ),
                Expanded(
                  child: _buildTimeInfo(
                    context,
                    'End',
                    '${shift.endTime.hour}:${shift.endTime.minute.toString().padLeft(2, '0')}',
                    Icons.access_time_filled,
                  ),
                ),
                Container(
                  width: 1,
                  height: 40,
                  color: AppTheme.textTertiary.withValues(alpha: 0.3),
                ),
                Expanded(
                  child: _buildTimeInfo(
                    context,
                    'Duration',
                    '${shift.hoursWorked.toStringAsFixed(1)}h',
                    Icons.timer,
                  ),
                ),
              ],
            ),
            if (shift.notes != null && shift.notes!.isNotEmpty) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppTheme.backgroundColor,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.note,
                      size: 16,
                      color: AppTheme.textSecondary,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        shift.notes!,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppTheme.textSecondary,
                            ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
            if (shift.location != null && shift.location!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(
                    Icons.location_on,
                    size: 16,
                    color: AppTheme.textSecondary,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      shift.location!,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppTheme.textSecondary,
                          ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTimeInfo(
      BuildContext context, String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          size: 20,
          color: AppTheme.textTertiary,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimary,
              ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppTheme.textSecondary,
              ),
        ),
      ],
    );
  }

  String _getDayName(DateTime date) {
    final days = [
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Sunday'
    ];
    return days[date.weekday - 1];
  }

  String _getDateString(DateTime date) {
    final months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }

  Color _getStatusColor(ShiftStatus status) {
    switch (status) {
      case ShiftStatus.scheduled:
        return AppTheme.warningColor;
      case ShiftStatus.confirmed:
        return AppTheme.primaryColor;
      case ShiftStatus.completed:
        return AppTheme.successColor;
      case ShiftStatus.cancelled:
        return AppTheme.errorColor;
    }
  }

  IconData _getStatusIcon(ShiftStatus status) {
    switch (status) {
      case ShiftStatus.scheduled:
        return Icons.schedule;
      case ShiftStatus.confirmed:
        return Icons.check_circle;
      case ShiftStatus.completed:
        return Icons.done_all;
      case ShiftStatus.cancelled:
        return Icons.cancel;
    }
  }
}
