import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';

class MyShiftsPage extends StatelessWidget {
  const MyShiftsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('My Shifts'),
      ),
      body: const Center(
        child: Text(
          'My Shifts Page - Coming Soon',
          style: TextStyle(fontSize: 18),
        ),
      ),
    );
  }
} 