import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../../features/auth/presentation/pages/login_page.dart';
import '../../features/auth/presentation/pages/signup_page.dart';
import '../../features/manager/presentation/pages/manager_dashboard_page.dart';
import '../../features/manager/presentation/pages/roster_management_page.dart';
import '../../features/manager/presentation/pages/employee_management_page.dart';
import '../../features/manager/presentation/pages/ai_assistant_page.dart';
import '../../features/manager/presentation/pages/analytics_page.dart';
import '../../features/employee/presentation/pages/employee_dashboard_page.dart';
import '../../features/employee/presentation/pages/my_shifts_page.dart';
import '../../features/employee/presentation/pages/availability_page.dart';
import '../../features/employee/presentation/pages/profile_page.dart';
import '../../features/shared/presentation/pages/splash_page.dart';
import '../../features/shared/presentation/pages/error_page.dart';

class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: '/',
    redirect: (context, state) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // Show splash while loading
      if (authProvider.isLoading) {
        return '/splash';
      }

      // If not authenticated, redirect to login
      if (!authProvider.isAuthenticated) {
        return '/login';
      }

      // If authenticated, redirect based on role
      if (authProvider.isManager) {
        return '/manager/dashboard';
      } else if (authProvider.isEmployee) {
        return '/employee/dashboard';
      }

      return null;
    },
    routes: [
      // Splash page
      GoRoute(
        path: '/splash',
        builder: (context, state) => const SplashPage(),
      ),

      // Auth routes
      GoRoute(
        path: '/login',
        builder: (context, state) => const LoginPage(),
      ),
      GoRoute(
        path: '/signup',
        builder: (context, state) => const SignupPage(),
      ),

      // Manager routes
      GoRoute(
        path: '/manager',
        redirect: (context, state) => '/manager/dashboard',
      ),
      GoRoute(
        path: '/manager/dashboard',
        builder: (context, state) => const ManagerDashboardPage(),
      ),
      GoRoute(
        path: '/manager/roster',
        builder: (context, state) => const RosterManagementPage(),
      ),
      GoRoute(
        path: '/manager/employees',
        builder: (context, state) => const EmployeeManagementPage(),
      ),
      GoRoute(
        path: '/manager/ai-assistant',
        builder: (context, state) => const AIAssistantPage(),
      ),
      GoRoute(
        path: '/manager/analytics',
        builder: (context, state) => const AnalyticsPage(),
      ),

      // Employee routes
      GoRoute(
        path: '/employee',
        redirect: (context, state) => '/employee/dashboard',
      ),
      GoRoute(
        path: '/employee/dashboard',
        builder: (context, state) => const EmployeeDashboardPage(),
      ),
      GoRoute(
        path: '/employee/shifts',
        builder: (context, state) => const MyShiftsPage(),
      ),
      GoRoute(
        path: '/employee/availability',
        builder: (context, state) => const AvailabilityPage(),
      ),
      GoRoute(
        path: '/employee/profile',
        builder: (context, state) => const ProfilePage(),
      ),

      // Error page
      GoRoute(
        path: '/error',
        builder: (context, state) => const ErrorPage(),
      ),
    ],
    errorBuilder: (context, state) => const ErrorPage(),
  );
}
