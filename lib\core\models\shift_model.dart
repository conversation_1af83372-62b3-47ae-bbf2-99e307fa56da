enum ShiftStatus { scheduled, confirmed, completed, cancelled }

enum ShiftType { morning, afternoon, evening, night, custom }

class ShiftModel {
  final String id;
  final String employeeId;
  final String employeeName;
  final DateTime startTime;
  final DateTime endTime;
  final ShiftType type;
  final ShiftStatus status;
  final String? notes;
  final String? location;
  final double? hourlyRate;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? createdBy;
  final Map<String, dynamic>? metadata;

  ShiftModel({
    required this.id,
    required this.employeeId,
    required this.employeeName,
    required this.startTime,
    required this.endTime,
    required this.type,
    this.status = ShiftStatus.scheduled,
    this.notes,
    this.location,
    this.hourlyRate,
    required this.createdAt,
    required this.updatedAt,
    this.createdBy,
    this.metadata,
  });

  factory ShiftModel.fromJson(Map<String, dynamic> json) {
    return ShiftModel(
      id: json['id'] as String,
      employeeId: json['employeeId'] as String,
      employeeName: json['employeeName'] as String,
      startTime: DateTime.parse(json['startTime'] as String),
      endTime: DateTime.parse(json['endTime'] as String),
      type: ShiftType.values.firstWhere(
        (e) => e.toString() == 'ShiftType.${json['type']}',
        orElse: () => ShiftType.custom,
      ),
      status: ShiftStatus.values.firstWhere(
        (e) => e.toString() == 'ShiftStatus.${json['status']}',
        orElse: () => ShiftStatus.scheduled,
      ),
      notes: json['notes'] as String?,
      location: json['location'] as String?,
      hourlyRate: json['hourlyRate'] as double?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      createdBy: json['createdBy'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'employeeId': employeeId,
      'employeeName': employeeName,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime.toIso8601String(),
      'type': type.toString().split('.').last,
      'status': status.toString().split('.').last,
      'notes': notes,
      'location': location,
      'hourlyRate': hourlyRate,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'createdBy': createdBy,
      'metadata': metadata,
    };
  }

  Duration get duration => endTime.difference(startTime);

  double get hoursWorked => duration.inMinutes / 60.0;

  bool get isToday {
    final now = DateTime.now();
    return startTime.year == now.year &&
        startTime.month == now.month &&
        startTime.day == now.day;
  }

  bool get isUpcoming {
    return startTime.isAfter(DateTime.now());
  }

  bool get isOngoing {
    final now = DateTime.now();
    return startTime.isBefore(now) && endTime.isAfter(now);
  }

  bool get isCompleted {
    return endTime.isBefore(DateTime.now());
  }

  ShiftModel copyWith({
    String? id,
    String? employeeId,
    String? employeeName,
    DateTime? startTime,
    DateTime? endTime,
    ShiftType? type,
    ShiftStatus? status,
    String? notes,
    String? location,
    double? hourlyRate,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    Map<String, dynamic>? metadata,
  }) {
    return ShiftModel(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      employeeName: employeeName ?? this.employeeName,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      type: type ?? this.type,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      location: location ?? this.location,
      hourlyRate: hourlyRate ?? this.hourlyRate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      metadata: metadata ?? this.metadata,
    );
  }
}
