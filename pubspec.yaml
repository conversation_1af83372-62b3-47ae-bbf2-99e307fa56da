name: quick_roster
description: A cross-platform roster management app with AI assistance for managers and employees.
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # State Management
  provider: ^6.1.1
  
  # UI Components
  cupertino_icons: ^1.0.2
  flutter_svg: ^2.0.9
  google_fonts: ^6.1.0
  
  # Navigation
  go_router: ^12.1.3
  
  # Local Storage
  shared_preferences: ^2.2.2
  sqflite: ^2.3.0
  
  # HTTP & API
  http: ^1.1.0
  dio: ^5.3.2
  
  # Date & Time
  intl: ^0.18.1
  table_calendar: ^3.0.9
  timezone: ^0.9.2
  
  # AI Integration
  flutter_tts: ^3.8.5
  
  # Notifications
  flutter_local_notifications: ^16.3.0
  
  # Charts & Data Visualization
  fl_chart: ^0.65.0
  
  # Image Handling
  image_picker: ^1.0.4
  
  # Authentication
  firebase_auth: ^4.15.3
  firebase_core: ^2.24.2
  cloud_firestore: ^4.13.6
  
  # Utilities
  uuid: ^4.2.1
  permission_handler: ^11.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/data/
  
  fonts:
    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins-Regular.ttf
        - asset: assets/fonts/Poppins-Medium.ttf
          weight: 500
        - asset: assets/fonts/Poppins-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Poppins-Bold.ttf
          weight: 700 