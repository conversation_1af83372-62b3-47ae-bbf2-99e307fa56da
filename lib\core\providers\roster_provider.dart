import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/shift_model.dart';
import '../models/availability_model.dart';
import '../models/user_model.dart';

class RosterProvider with ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  List<ShiftModel> _shifts = [];
  List<AvailabilityModel> _availabilities = [];
  List<UserModel> _employees = [];
  bool _isLoading = false;
  String? _error;

  List<ShiftModel> get shifts => _shifts;
  List<AvailabilityModel> get availabilities => _availabilities;
  List<UserModel> get employees => _employees;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Filtered getters
  List<ShiftModel> get todayShifts =>
      _shifts.where((shift) => shift.isToday).toList();
  List<ShiftModel> get upcomingShifts =>
      _shifts.where((shift) => shift.isUpcoming).toList();
  List<ShiftModel> get ongoingShifts =>
      _shifts.where((shift) => shift.isOngoing).toList();
  List<ShiftModel> get completedShifts =>
      _shifts.where((shift) => shift.isCompleted).toList();

  Future<void> loadShifts({DateTime? startDate, DateTime? endDate}) async {
    try {
      _isLoading = true;
      notifyListeners();

      Query query = _firestore.collection('shifts');

      if (startDate != null) {
        query = query.where('startTime',
            isGreaterThanOrEqualTo: startDate.toIso8601String());
      }
      if (endDate != null) {
        query = query.where('startTime',
            isLessThanOrEqualTo: endDate.toIso8601String());
      }

      final snapshot = await query.get();
      _shifts = snapshot.docs
          .map((doc) => ShiftModel.fromJson({
                'id': doc.id,
                ...doc.data() as Map<String, dynamic>,
              }))
          .toList();

      // Sort by start time
      _shifts.sort((a, b) => a.startTime.compareTo(b.startTime));
    } catch (e) {
      _error = 'Failed to load shifts: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> loadAvailabilities() async {
    try {
      _isLoading = true;
      notifyListeners();

      final snapshot = await _firestore.collection('availabilities').get();
      _availabilities = snapshot.docs
          .map((doc) => AvailabilityModel.fromJson({
                'id': doc.id,
                ...doc.data(),
              }))
          .toList();
    } catch (e) {
      _error = 'Failed to load availabilities: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> loadEmployees() async {
    try {
      _isLoading = true;
      notifyListeners();

      final snapshot = await _firestore
          .collection('users')
          .where('role', isEqualTo: 'employee')
          .where('isActive', isEqualTo: true)
          .get();

      _employees = snapshot.docs
          .map((doc) => UserModel.fromJson({
                'id': doc.id,
                ...doc.data(),
              }))
          .toList();
    } catch (e) {
      _error = 'Failed to load employees: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> createShift(ShiftModel shift) async {
    try {
      _isLoading = true;
      notifyListeners();

      final docRef = await _firestore.collection('shifts').add(shift.toJson());
      final newShift = shift.copyWith(id: docRef.id);

      _shifts.add(newShift);
      _shifts.sort((a, b) => a.startTime.compareTo(b.startTime));

      return true;
    } catch (e) {
      _error = 'Failed to create shift: $e';
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> updateShift(ShiftModel shift) async {
    try {
      _isLoading = true;
      notifyListeners();

      await _firestore
          .collection('shifts')
          .doc(shift.id)
          .update(shift.toJson());

      final index = _shifts.indexWhere((s) => s.id == shift.id);
      if (index != -1) {
        _shifts[index] = shift;
        _shifts.sort((a, b) => a.startTime.compareTo(b.startTime));
      }

      return true;
    } catch (e) {
      _error = 'Failed to update shift: $e';
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> deleteShift(String shiftId) async {
    try {
      _isLoading = true;
      notifyListeners();

      await _firestore.collection('shifts').doc(shiftId).delete();
      _shifts.removeWhere((shift) => shift.id == shiftId);

      return true;
    } catch (e) {
      _error = 'Failed to delete shift: $e';
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> createAvailability(AvailabilityModel availability) async {
    try {
      _isLoading = true;
      notifyListeners();

      final docRef = await _firestore
          .collection('availabilities')
          .add(availability.toJson());
      final newAvailability = availability.copyWith(id: docRef.id);

      _availabilities.add(newAvailability);

      return true;
    } catch (e) {
      _error = 'Failed to create availability: $e';
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> updateAvailability(AvailabilityModel availability) async {
    try {
      _isLoading = true;
      notifyListeners();

      await _firestore
          .collection('availabilities')
          .doc(availability.id)
          .update(availability.toJson());

      final index = _availabilities.indexWhere((a) => a.id == availability.id);
      if (index != -1) {
        _availabilities[index] = availability;
      }

      return true;
    } catch (e) {
      _error = 'Failed to update availability: $e';
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> deleteAvailability(String availabilityId) async {
    try {
      _isLoading = true;
      notifyListeners();

      await _firestore
          .collection('availabilities')
          .doc(availabilityId)
          .delete();
      _availabilities
          .removeWhere((availability) => availability.id == availabilityId);

      return true;
    } catch (e) {
      _error = 'Failed to delete availability: $e';
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Helper methods
  List<ShiftModel> getShiftsForEmployee(String employeeId) {
    return _shifts.where((shift) => shift.employeeId == employeeId).toList();
  }

  List<ShiftModel> getShiftsForDate(DateTime date) {
    return _shifts.where((shift) {
      return shift.startTime.year == date.year &&
          shift.startTime.month == date.month &&
          shift.startTime.day == date.day;
    }).toList();
  }

  List<ShiftModel> getShiftsForDateRange(DateTime startDate, DateTime endDate) {
    return _shifts.where((shift) {
      return shift.startTime
              .isAfter(startDate.subtract(const Duration(days: 1))) &&
          shift.startTime.isBefore(endDate.add(const Duration(days: 1)));
    }).toList();
  }

  AvailabilityModel? getAvailabilityForEmployee(String employeeId) {
    try {
      return _availabilities
          .firstWhere((availability) => availability.employeeId == employeeId);
    } catch (e) {
      return null;
    }
  }

  UserModel? getEmployeeById(String employeeId) {
    try {
      return _employees.firstWhere((employee) => employee.id == employeeId);
    } catch (e) {
      return null;
    }
  }

  double getTotalHoursForEmployee(String employeeId,
      {DateTime? startDate, DateTime? endDate}) {
    List<ShiftModel> employeeShifts = getShiftsForEmployee(employeeId);

    if (startDate != null && endDate != null) {
      employeeShifts = employeeShifts.where((shift) {
        return shift.startTime
                .isAfter(startDate.subtract(const Duration(days: 1))) &&
            shift.startTime.isBefore(endDate.add(const Duration(days: 1)));
      }).toList();
    }

    return employeeShifts.fold(
        0.0, (total, shift) => total + shift.hoursWorked);
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  Future<void> refresh() async {
    await Future.wait([
      loadShifts(),
      loadAvailabilities(),
      loadEmployees(),
    ]);
  }
}
