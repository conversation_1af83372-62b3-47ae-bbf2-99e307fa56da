# Quick Roster - Setup Guide

This guide will help you set up the Quick Roster Flutter app on your local machine.

## Prerequisites

Before you begin, make sure you have the following installed:

- **Flutter SDK** (3.0 or higher)
- **Dart SDK** (3.0 or higher)
- **Android Studio** or **VS Code**
- **Git**

## Installation Steps

### 1. Clone the Repository

```bash
git clone https://github.com/yourusername/quick-roster.git
cd quick-roster
```

### 2. Install Dependencies

```bash
flutter pub get
```

### 3. Firebase Setup

#### Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Add project"
3. Enter a project name (e.g., "quick-roster")
4. Follow the setup wizard

#### Enable Authentication

1. In Firebase Console, go to "Authentication"
2. Click "Get started"
3. Go to "Sign-in method"
4. Enable "Email/Password"

#### Enable Firestore

1. In Firebase Console, go to "Firestore Database"
2. Click "Create database"
3. Choose "Start in test mode" (for development)
4. Select a location

#### Download Configuration Files

1. Go to Project Settings (gear icon)
2. Scroll down to "Your apps"
3. Add Android app:
   - Package name: `com.quickroster.app`
   - Download `google-services.json`
   - Place in `android/app/`
4. Add iOS app:
   - Bundle ID: `com.quickroster.app`
   - Download `GoogleService-Info.plist`
   - Place in `ios/Runner/`

### 4. AI Configuration

#### Get OpenAI API Key

1. Go to [OpenAI Platform](https://platform.openai.com/)
2. Sign up or log in
3. Go to "API Keys"
4. Create a new API key

#### Update API Key

1. Open `lib/core/providers/ai_provider.dart`
2. Replace `YOUR_OPENAI_API_KEY` with your actual API key:

```dart
static const String _apiKey = 'sk-your-actual-api-key-here';
```

### 5. Run the App

#### For Android

```bash
flutter run
```

#### For iOS

```bash
flutter run
```

**Note**: For iOS, you'll need a Mac and Xcode installed.

## Development Setup

### Code Structure

The app follows a feature-based architecture:

```
lib/
├── core/           # Core functionality
├── features/       # Feature modules
│   ├── auth/      # Authentication
│   ├── manager/   # Manager features
│   ├── employee/  # Employee features
│   └── shared/    # Shared components
└── main.dart      # App entry point
```

### State Management

The app uses Provider for state management:

- `AuthProvider`: Handles authentication
- `RosterProvider`: Manages shifts and availability
- `AIProvider`: Handles AI features

### Testing

Run tests with:

```bash
flutter test
```

## Common Issues

### Firebase Configuration

If you get Firebase-related errors:

1. Make sure configuration files are in the correct locations
2. Verify package name/bundle ID matches Firebase project
3. Check that Authentication and Firestore are enabled

### AI Features

If AI features don't work:

1. Verify OpenAI API key is correct
2. Check API key has sufficient credits
3. Ensure internet connection is available

### Build Issues

If you encounter build issues:

```bash
flutter clean
flutter pub get
flutter run
```

## Next Steps

1. **Customize the UI**: Modify colors and themes in `lib/core/theme/app_theme.dart`
2. **Add Features**: Implement additional functionality
3. **Deploy**: Build for production and deploy to app stores

## Support

For issues and questions:

1. Check the [README.md](README.md) for more details
2. Create an issue on GitHub
3. Contact the development team

---

Happy coding! 🚀 