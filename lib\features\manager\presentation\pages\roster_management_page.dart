import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';

class RosterManagementPage extends StatelessWidget {
  const RosterManagementPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Roster Management'),
      ),
      body: const Center(
        child: Text(
          'Roster Management Page - Coming Soon',
          style: TextStyle(fontSize: 18),
        ),
      ),
    );
  }
} 