import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/providers/auth_provider.dart';
import '../../../../core/providers/roster_provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../widgets/employee_shift_card.dart';
import '../widgets/employee_quick_action.dart';

class EmployeeDashboardPage extends StatefulWidget {
  const EmployeeDashboardPage({super.key});

  @override
  State<EmployeeDashboardPage> createState() => _EmployeeDashboardPageState();
}

class _EmployeeDashboardPageState extends State<EmployeeDashboardPage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final rosterProvider =
          Provider.of<RosterProvider>(context, listen: false);
      rosterProvider.refresh();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('My Dashboard'),
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {
              // TODO: Implement notifications
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Notifications coming soon')),
              );
            },
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'profile':
                  context.go('/employee/profile');
                  break;
                case 'settings':
                  // TODO: Navigate to settings
                  break;
                case 'logout':
                  _handleLogout();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'profile',
                child: Row(
                  children: [
                    Icon(Icons.person_outline),
                    SizedBox(width: 8),
                    Text('Profile'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings_outlined),
                    SizedBox(width: 8),
                    Text('Settings'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'logout',
                child: Row(
                  children: [
                    Icon(Icons.logout),
                    SizedBox(width: 8),
                    Text('Logout'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          final rosterProvider =
              Provider.of<RosterProvider>(context, listen: false);
          await rosterProvider.refresh();
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Welcome Section
              Consumer<AuthProvider>(
                builder: (context, authProvider, child) {
                  return Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [
                          AppTheme.secondaryColor,
                          AppTheme.secondaryLight,
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Row(
                      children: [
                        CircleAvatar(
                          radius: 30,
                          backgroundColor: Colors.white.withValues(alpha: 0.2),
                          child: const Icon(
                            Icons.person,
                            size: 30,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Welcome back,',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.copyWith(
                                      color:
                                          Colors.white.withValues(alpha: 0.8),
                                    ),
                              ),
                              Text(
                                authProvider.currentUser?.name ?? 'Employee',
                                style: Theme.of(context)
                                    .textTheme
                                    .headlineSmall
                                    ?.copyWith(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                    ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),

              const SizedBox(height: 24),

              // Today's Shift
              Text(
                'Today\'s Shift',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimary,
                    ),
              ),
              const SizedBox(height: 16),

              Consumer<RosterProvider>(
                builder: (context, rosterProvider, child) {
                  final authProvider =
                      Provider.of<AuthProvider>(context, listen: false);
                  final todayShifts = rosterProvider
                      .getShiftsForEmployee(
                        authProvider.currentUser?.id ?? '',
                      )
                      .where((shift) => shift.isToday)
                      .toList();

                  if (todayShifts.isEmpty) {
                    return Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: AppTheme.surfaceColor,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: AppTheme.textTertiary.withValues(alpha: 0.2),
                        ),
                      ),
                      child: Center(
                        child: Column(
                          children: [
                            const Icon(
                              Icons.event_busy,
                              size: 48,
                              color: AppTheme.textTertiary,
                            ),
                            const SizedBox(height: 8),
                            const Text(
                              'No shifts today',
                              style: TextStyle(
                                color: AppTheme.textSecondary,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Enjoy your day off!',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    color: AppTheme.textTertiary,
                                  ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }

                  return ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: todayShifts.length,
                    itemBuilder: (context, index) {
                      return EmployeeShiftCard(shift: todayShifts[index]);
                    },
                  );
                },
              ),

              const SizedBox(height: 32),

              // Quick Actions
              Text(
                'Quick Actions',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimary,
                    ),
              ),
              const SizedBox(height: 16),

              GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 1.5,
                children: [
                  EmployeeQuickAction(
                    title: 'My Shifts',
                    subtitle: 'View all shifts',
                    icon: Icons.schedule,
                    color: AppTheme.primaryColor,
                    onTap: () => context.go('/employee/shifts'),
                  ),
                  EmployeeQuickAction(
                    title: 'Availability',
                    subtitle: 'Set preferences',
                    icon: Icons.calendar_today,
                    color: AppTheme.secondaryColor,
                    onTap: () => context.go('/employee/availability'),
                  ),
                  EmployeeQuickAction(
                    title: 'Profile',
                    subtitle: 'Update details',
                    icon: Icons.person_outline,
                    color: AppTheme.accentColor,
                    onTap: () => context.go('/employee/profile'),
                  ),
                  EmployeeQuickAction(
                    title: 'Notifications',
                    subtitle: 'Manage alerts',
                    icon: Icons.notifications_outlined,
                    color: AppTheme.warningColor,
                    onTap: () {
                      // TODO: Implement notifications
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                            content: Text('Notifications coming soon')),
                      );
                    },
                  ),
                ],
              ),

              const SizedBox(height: 32),

              // Upcoming Shifts
              Text(
                'Upcoming Shifts',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimary,
                    ),
              ),
              const SizedBox(height: 16),

              Consumer<RosterProvider>(
                builder: (context, rosterProvider, child) {
                  final authProvider =
                      Provider.of<AuthProvider>(context, listen: false);
                  final upcomingShifts = rosterProvider
                      .getShiftsForEmployee(
                        authProvider.currentUser?.id ?? '',
                      )
                      .where((shift) => shift.isUpcoming)
                      .take(3)
                      .toList();

                  if (upcomingShifts.isEmpty) {
                    return Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: AppTheme.surfaceColor,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: AppTheme.textTertiary.withValues(alpha: 0.2),
                        ),
                      ),
                      child: Center(
                        child: Column(
                          children: [
                            const Icon(
                              Icons.schedule_outlined,
                              size: 48,
                              color: AppTheme.textTertiary,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'No upcoming shifts',
                              style: Theme.of(context)
                                  .textTheme
                                  .titleMedium
                                  ?.copyWith(
                                    color: AppTheme.textSecondary,
                                    fontWeight: FontWeight.w600,
                                  ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Check back later for new assignments',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    color: AppTheme.textTertiary,
                                  ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }

                  return ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: upcomingShifts.length,
                    itemBuilder: (context, index) {
                      return EmployeeShiftCard(shift: upcomingShifts[index]);
                    },
                  );
                },
              ),

              const SizedBox(height: 32),

              // Weekly Summary
              Consumer<RosterProvider>(
                builder: (context, rosterProvider, child) {
                  final authProvider =
                      Provider.of<AuthProvider>(context, listen: false);
                  final thisWeekShifts = rosterProvider
                      .getShiftsForEmployee(
                    authProvider.currentUser?.id ?? '',
                  )
                      .where((shift) {
                    final now = DateTime.now();
                    final startOfWeek =
                        now.subtract(Duration(days: now.weekday - 1));
                    final endOfWeek = startOfWeek.add(const Duration(days: 6));
                    return shift.startTime.isAfter(
                            startOfWeek.subtract(const Duration(days: 1))) &&
                        shift.startTime
                            .isBefore(endOfWeek.add(const Duration(days: 1)));
                  }).toList();

                  final totalHours = thisWeekShifts.fold(
                    0.0,
                    (sum, shift) => sum + shift.hoursWorked,
                  );

                  return Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: AppTheme.surfaceColor,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.05),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'This Week',
                          style:
                              Theme.of(context).textTheme.titleLarge?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.textPrimary,
                                  ),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            Expanded(
                              child: _buildSummaryItem(
                                'Shifts',
                                '${thisWeekShifts.length}',
                                Icons.schedule,
                                AppTheme.primaryColor,
                              ),
                            ),
                            Expanded(
                              child: _buildSummaryItem(
                                'Hours',
                                '${totalHours.toStringAsFixed(1)}h',
                                Icons.access_time,
                                AppTheme.secondaryColor,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  );
                },
              ),

              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryItem(
      String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            color: color,
            size: 24,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimary,
              ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondary,
              ),
        ),
      ],
    );
  }

  Future<void> _handleLogout() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Logout'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      await authProvider.signOut();
    }
  }
}
