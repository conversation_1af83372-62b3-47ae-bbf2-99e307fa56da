import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';

class AvailabilityPage extends StatelessWidget {
  const AvailabilityPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('My Availability'),
      ),
      body: const Center(
        child: Text(
          'Availability Page - Coming Soon',
          style: TextStyle(fontSize: 18),
        ),
      ),
    );
  }
} 